import { Product, ProductAvailability, AvailabilityType, DayOfWeek } from "@prisma/client";
import { useMemo } from "react";

function useProductAvailability(
  product: Product & { availability: ProductAvailability[] }
) {
  const { isAvailable, message } = useMemo(() => {
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

    // Map JavaScript day numbers to DayOfWeek enum
    const dayOfWeekMap: Record<number, DayOfWeek> = {
      0: DayOfWeek.SUNDAY,
      1: DayOfWeek.MONDAY,
      2: DayOfWeek.TUESDAY,
      3: DayOfWeek.WEDNESDAY,
      4: DayOfWeek.THURSDAY,
      5: DayOfWeek.FRIDAY,
      6: DayOfWeek.SATURDAY,
    };

    const currentDayOfWeek = dayOfWeekMap[currentDay];

    // Start with the product's default availability
    let productIsAvailable = product.alwaysAvailable;
    let availabilityMessage = "";

    // Check all availability schedules to see if any override the default
    for (const schedule of product.availability) {
      let scheduleApplies = false;

      switch (schedule.type) {
        case AvailabilityType.DAILY_RECURRING:
          // Applies every day
          scheduleApplies = true;
          break;

        case AvailabilityType.WEEKLY_RECURRING:
          // Applies only on specific day of week
          scheduleApplies = schedule.dayOfWeek === currentDayOfWeek;
          break;

        case AvailabilityType.DATE_RANGE:
          // Applies within date range
          if (schedule.startDate && schedule.endDate) {
            const startDate = new Date(schedule.startDate);
            const endDate = new Date(schedule.endDate);
            // Set time to start/end of day for proper comparison
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
            scheduleApplies = now >= startDate && now <= endDate;
          }
          break;

        case AvailabilityType.ONE_TIME:
          // Applies only on specific date
          if (schedule.startDate) {
            const scheduleDate = new Date(schedule.startDate);
            const todayStart = new Date(now);
            todayStart.setHours(0, 0, 0, 0);
            const todayEnd = new Date(now);
            todayEnd.setHours(23, 59, 59, 999);
            scheduleApplies = scheduleDate >= todayStart && scheduleDate <= todayEnd;
          }
          break;
      }

      // If this schedule applies, check time constraints
      if (scheduleApplies) {
        let timeMatches = true;

        // Check time constraints if specified
        if (schedule.startTime && schedule.endTime) {
          timeMatches = currentTime >= schedule.startTime && currentTime <= schedule.endTime;
        }

        if (timeMatches) {
          // This schedule overrides the default availability
          productIsAvailable = schedule.isAvailable;

          // Set appropriate message based on availability and timing
          if (!schedule.isAvailable) {
            if (schedule.beforeMessage && currentTime < schedule.startTime!) {
              availabilityMessage = schedule.beforeMessage;
            } else if (schedule.afterMessage && currentTime > schedule.endTime!) {
              availabilityMessage = schedule.afterMessage;
            } else {
              availabilityMessage = "This product is currently unavailable";
            }
          }

          // Break after first matching schedule (schedules should be ordered by priority)
          break;
        } else if (!schedule.isAvailable) {
          // Schedule applies but we're outside time range - check for before/after messages
          if (schedule.beforeMessage && schedule.startTime && currentTime < schedule.startTime) {
            availabilityMessage = schedule.beforeMessage;
          } else if (schedule.afterMessage && schedule.endTime && currentTime > schedule.endTime) {
            availabilityMessage = schedule.afterMessage;
          }
        }
      }
    }

    // Default message if product is unavailable and no specific message is set
    if (!productIsAvailable && !availabilityMessage) {
      availabilityMessage = "This product is currently unavailable";
    }

    return {
      isAvailable: productIsAvailable,
      message: availabilityMessage,
    };
  }, [product.alwaysAvailable, product.availability]);

  return { isAvailable, message };
}

export default useProductAvailability;
