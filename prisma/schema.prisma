// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL") // uses connection pooling
}

model Address {
  id       String       @id @default(cuid())
  label    String
  name     String
  home     String
  userId   Int
  user     User         @relation(fields: [userId], references: [id])
  zoneId   String
  zone     DeliveryZone @relation(fields: [zoneId], references: [id])
  orders   Order[]
  phone    String
  location String?

  // @@unique([userId, zoneId])
  cart Cart[]
}

enum Role {
  USER
  ADMIN
  MAINTAINER
  SUPER_ADMIN
  DELIVERY_MAN
}

model User {
  id        Int       @id @default(autoincrement())
  createdAt DateTime  @default(now())
  email     String    @unique
  name      String
  avatar    String?
  phone     String?
  address   Address[]
  cart      Cart?

  role        Role        @default(USER)
  pickedItems OrderItem[]
  deliveries  Order[]     @relation(name: "<PERSON>iveryMan")
  orders      Order[]
  shop        Shop?

  notifications     Notification[]
  pushSubscriptions PushSubscription[]
}

model Shop {
  id        String    @id @default(cuid())
  name      String
  createdAt DateTime  @default(now())
  slug      String    @unique
  location  String
  products  Product[]

  owner   User? @relation(fields: [ownerId], references: [id])
  ownerId Int?  @unique
}

model Category {
  id   String  @id @default(cuid())
  name String
  nam  String?

  titleEn String?
  titleBn String?
  titleBe String?

  descriptionEn String?
  descriptionBn String?

  slug           String     @unique
  isBase         Boolean    @default(false)
  hide           Boolean    @default(false)
  image          String?
  products       Product[]
  parentId       String?
  parentCategory Category?  @relation("Subcategories", fields: [parentId], references: [id], onDelete: Cascade)
  subCategories  Category[] @relation("Subcategories")
  position       Int        @default(100)
  featured       Boolean    @default(false)
}

model ProductImage {
  id        String  @id @default(cuid())
  url       String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId String
}

model Product {
  id          String         @id @default(cuid())
  slug        String?
  name        String
  nam         String?
  description String
  biboron     String?
  titleBe     String?
  images      ProductImage[]
  createdAt   DateTime       @default(now())
  updatedAt   DateTime?      @updatedAt

  position Int     @default(500)
  featured Boolean @default(false)

  amount   Int // Amount in terms of unit, like 500gm , 1ltr, 1kg, 1pcs
  supply   Int
  quantity Int @default(1) // Quantity per order like: 12 eggs, 1 container oil (5ltr)
  maxOrder Int @default(10) // Maximum order quantity, null for unlimited

  price           Int
  discount        Int
  sourcePrice     Int
  hide            Boolean @default(false)
  alwaysAvailable Boolean @default(true) // If true, product is always available unless overridden by `availability` entries
  extraCharge     Int     @default(0)

  shopId String?
  shop   Shop?   @relation(fields: [shopId], references: [id])

  categoryId String?
  category   Category? @relation(fields: [categoryId], references: [id])

  unitId String
  unit   QuantityUnit @relation(fields: [unitId], references: [id])

  cartItems  CartItem[]
  orderItems OrderItem[]

  company   Company? @relation(fields: [companyId], references: [id])
  companyId String?

  availability ProductAvailability[] // Link to specific availability schedules
}

model Company {
  id       String    @id @default(cuid())
  name     String
  nam      String?
  slug     String    @unique
  products Product[]
}

model DeliveryZone {
  id         String         @id @default(cuid())
  name       String
  nam        String?
  slug       String         @unique
  charge     Int            @default(0) // Regular Delivery Charge
  express    Int            @default(0) // Express Delivery Change
  address    Address[]
  isBase     Boolean        @default(false)
  parentId   String?
  parentZone DeliveryZone?  @relation("Subzones", fields: [parentId], references: [id], onDelete: Cascade)
  subZones   DeliveryZone[] @relation("Subzones")
}

model QuantityUnit {
  id      String    @id @default(cuid())
  slug    String    @unique
  full    String
  bangla  String?
  product Product[]
}

enum OrderStatus {
  PENDING
  CANCELLED
  CONFIRMED
  PACKED
  SHIPPING
  DELIVERED
  RETURNED
}

model Order {
  id        Int       @id @default(autoincrement())
  buyerId   Int
  buyer     User      @relation(fields: [buyerId], references: [id])
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  subTotal Int
  shipping Int
  discount Int
  status   OrderStatus @default(PENDING)

  addressId String
  address   Address @relation(fields: [addressId], references: [id])

  deliveryManId Int?
  deliveryMan   User? @relation(name: "DeliveryMan", fields: [deliveryManId], references: [id])

  orderItems OrderItem[]
  timeline   OrderTimeline[]
}

model OrderTimeline {
  id        Int      @id @default(autoincrement())
  orderId   Int
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  status OrderStatus
  note   String?

  @@unique([orderId, status])
}

model OrderItem {
  id        Int      @id @default(autoincrement())
  orderId   Int
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  productId String
  product   Product   @relation(fields: [productId], references: [id])
  quantity  Int
  price     Int
  picked    Boolean   @default(false)
  pickedAt  DateTime?
  pickerId  Int?
  picker    User?     @relation(fields: [pickerId], references: [id])
}

model Cart {
  id        String    @id @default(cuid())
  userId    Int?      @unique
  user      User?     @relation(fields: [userId], references: [id])
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
  addressId String?
  address   Address?  @relation(fields: [addressId], references: [id])

  items CartItem[]
}

model CartItem {
  id        Int      @id @default(autoincrement())
  cartId    String
  cart      Cart     @relation(fields: [cartId], references: [id])
  createdAt DateTime @default(now())

  productId String
  product   Product @relation(fields: [productId], references: [id])
  quantity  Int

  @@unique([cartId, productId])
}

model Campaign {
  id String @id @default(cuid())

  title     String
  body      String
  imageUrl  String?
  targetUrl String?
  iconUrl   String?

  data         Json?
  scheduledFor DateTime @default(now())

  isSent Boolean   @default(false)
  sentAt DateTime?

  targetAudience Json?

  notifications Notification[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Notification {
  id         String    @id @default(cuid())
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId     Int
  campaign   Campaign? @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  campaignId String?

  title     String
  body      String
  imageUrl  String?
  targetUrl String?
  iconUrl   String?
  data      Json?

  isSeen    Boolean   @default(false)
  seenAt    DateTime?
  isClicked Boolean   @default(false)
  clickedAt DateTime?

  createdAt DateTime @default(now())

  @@unique([userId, campaignId])
}

model PushSubscription {
  id       String @id @default(cuid())
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId   Int
  endpoint String @unique
  p256dh   String
  auth     String

  createdAt DateTime @default(now())
}

enum DayOfWeek {
  SUNDAY
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
}

model ProductAvailability {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Type of availability schedule
  type AvailabilityType @default(DAILY_RECURRING)

  // For DAILY_RECURRING and WEEKLY_RECURRING
  dayOfWeek DayOfWeek? // Null for DAILY_RECURRING (applies every day)
  startTime String? // e.g., "09:00" (HH:MM format)
  endTime   String? // e.g., "17:00" (HH:MM format)

  // For DATE_RANGE and ONE_TIME
  startDate DateTime?
  endDate   DateTime?

  // If the entry makes the product available or unavailable
  isAvailable Boolean @default(true) // true for available, false for unavailable

  // Optional message to display when unavailable (for soft popup)
  beforeMessage String?
  afterMessage  String?

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  // Ensures uniqueness for a product on a given day/time for daily/weekly schedules
  @@unique([productId, type, dayOfWeek, startTime, endTime], name: "product_time_unique_constraint")
  // Ensures uniqueness for a product within a date range for date-based schedules
  @@unique([productId, type, startDate, endDate], name: "product_date_unique_constraint")
}

enum AvailabilityType {
  DAILY_RECURRING // e.g., available every day from 9 AM to 5 PM
  WEEKLY_RECURRING // e.g., available only on Mondays and Wednesdays from 10 AM to 6 PM
  DATE_RANGE // e.g., available from Oct 1st to Oct 15th
  ONE_TIME // e.g., available on Dec 25th from 8 AM to 12 PM
}
